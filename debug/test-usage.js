/**
 * 测试Token Multiplier Transformer功能
 */

const TokenMultiplierTransformer = require('./token-multiplier-transformer.js');

// 测试用例
function runTests() {
  console.log('开始测试 Token Multiplier Transformer...\n');

  // 测试1: 基本usage修改
  console.log('=== 测试1: 基本usage修改 ===');
  const transformer1 = new TokenMultiplierTransformer({ multiplier: 2.0 });
  
  const originalUsage = {
    completion_tokens: 100,
    prompt_tokens: 200,
    total_tokens: 300
  };
  
  const modifiedUsage = transformer1.multiplyUsage(originalUsage);
  console.log('原始usage:', originalUsage);
  console.log('修改后usage:', modifiedUsage);
  console.log('验证结果:', 
    modifiedUsage.completion_tokens === 200 &&
    modifiedUsage.prompt_tokens === 400 &&
    modifiedUsage.total_tokens === 600 ? '✅ 通过' : '❌ 失败'
  );
  console.log();

  // 测试2: Anthropic格式usage修改
  console.log('=== 测试2: Anthropic格式usage修改 ===');
  const transformer2 = new TokenMultiplierTransformer({ multiplier: 1.5 });
  
  const anthropicUsage = {
    input_tokens: 288,
    output_tokens: 503
  };
  
  const modifiedAnthropicUsage = transformer2.multiplyUsage(anthropicUsage);
  console.log('原始Anthropic usage:', anthropicUsage);
  console.log('修改后Anthropic usage:', modifiedAnthropicUsage);
  console.log('验证结果:', 
    modifiedAnthropicUsage.input_tokens === 432 &&
    modifiedAnthropicUsage.output_tokens === 755 ? '✅ 通过' : '❌ 失败'
  );
  console.log();

  // 测试3: 模拟OpenAI响应处理
  console.log('=== 测试3: 模拟OpenAI响应处理 ===');
  const transformer3 = new TokenMultiplierTransformer({ multiplier: 1.2 });
  
  const mockOpenAIResponse = {
    id: "chatcmpl-123",
    object: "chat.completion",
    created: 1677652288,
    model: "gpt-3.5-turbo",
    choices: [{
      index: 0,
      message: {
        role: "assistant",
        content: "Hello! How can I help you today?"
      },
      finish_reason: "stop"
    }],
    usage: {
      prompt_tokens: 13,
      completion_tokens: 7,
      total_tokens: 20
    }
  };

  // 模拟响应处理
  const processedResponse = JSON.parse(JSON.stringify(mockOpenAIResponse));
  if (processedResponse.usage) {
    processedResponse.usage = transformer3.multiplyUsage(processedResponse.usage);
  }

  console.log('原始OpenAI响应usage:', mockOpenAIResponse.usage);
  console.log('处理后OpenAI响应usage:', processedResponse.usage);
  console.log('验证结果:', 
    processedResponse.usage.prompt_tokens === 16 &&
    processedResponse.usage.completion_tokens === 8 &&
    processedResponse.usage.total_tokens === 24 ? '✅ 通过' : '❌ 失败'
  );
  console.log();

  // 测试4: 模拟Anthropic响应处理
  console.log('=== 测试4: 模拟Anthropic响应处理 ===');
  const transformer4 = new TokenMultiplierTransformer({ multiplier: 1.8 });
  
  const mockAnthropicResponse = {
    content: [{
      text: "Hi! My name is Claude.",
      type: "text"
    }],
    id: "msg_013Zva2CMHLNnXjNJJKqJ2EF",
    model: "claude-sonnet-4-20250514",
    role: "assistant",
    stop_reason: "end_turn",
    stop_sequence: null,
    type: "message",
    usage: {
      input_tokens: 2095,
      output_tokens: 503
    }
  };

  // 模拟响应处理
  const processedAnthropicResponse = JSON.parse(JSON.stringify(mockAnthropicResponse));
  if (processedAnthropicResponse.usage) {
    processedAnthropicResponse.usage = transformer4.multiplyUsage(processedAnthropicResponse.usage);
  }

  console.log('原始Anthropic响应usage:', mockAnthropicResponse.usage);
  console.log('处理后Anthropic响应usage:', processedAnthropicResponse.usage);
  console.log('验证结果:', 
    processedAnthropicResponse.usage.input_tokens === 3771 &&
    processedAnthropicResponse.usage.output_tokens === 905 ? '✅ 通过' : '❌ 失败'
  );
  console.log();

  // 测试5: 边界情况测试
  console.log('=== 测试5: 边界情况测试 ===');
  const transformer5 = new TokenMultiplierTransformer({ multiplier: 1.3 });
  
  // 测试空usage
  const emptyUsage = transformer5.multiplyUsage(null);
  console.log('空usage处理:', emptyUsage === null ? '✅ 通过' : '❌ 失败');
  
  // 测试无效usage
  const invalidUsage = transformer5.multiplyUsage("invalid");
  console.log('无效usage处理:', invalidUsage === "invalid" ? '✅ 通过' : '❌ 失败');
  
  // 测试部分字段
  const partialUsage = {
    completion_tokens: 50,
    // 缺少其他字段
  };
  const processedPartialUsage = transformer5.multiplyUsage(partialUsage);
  console.log('部分字段usage处理:', 
    processedPartialUsage.completion_tokens === 65 && 
    !processedPartialUsage.prompt_tokens ? '✅ 通过' : '❌ 失败'
  );
  console.log();

  console.log('所有测试完成！');
}

// 运行测试
if (require.main === module) {
  runTests();
}

module.exports = { runTests };
