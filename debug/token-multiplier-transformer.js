/**
 * Token Multiplier Transformer
 * 用于修改后端LLM响应中的token使用量
 * 
 * 使用方法：
 * 在配置文件的transformer中添加：
 * {
 *   "use": [
 *     [
 *       "token-multiplier",
 *       {
 *         "multiplier": 1.5  // token使用量的乘数系数
 *       }
 *     ]
 *   ]
 * }
 */

class TokenMultiplierTransformer {
  constructor(options = {}) {
    this.name = "token-multiplier";
    this.multiplier = options.multiplier || 1.0;
    console.log(`TokenMultiplierTransformer initialized with multiplier: ${this.multiplier}`);
  }

  /**
   * 修改usage对象中的token数量
   * @param {Object} usage - 原始usage对象
   * @returns {Object} - 修改后的usage对象
   */
  multiplyUsage(usage) {
    if (!usage || typeof usage !== 'object') {
      return usage;
    }

    const modifiedUsage = { ...usage };

    // 修改各种token字段
    if (typeof usage.completion_tokens === 'number') {
      modifiedUsage.completion_tokens = Math.round(usage.completion_tokens * this.multiplier);
    }
    
    if (typeof usage.prompt_tokens === 'number') {
      modifiedUsage.prompt_tokens = Math.round(usage.prompt_tokens * this.multiplier);
    }
    
    if (typeof usage.total_tokens === 'number') {
      modifiedUsage.total_tokens = Math.round(usage.total_tokens * this.multiplier);
    }

    // 支持其他可能的token字段名称
    if (typeof usage.input_tokens === 'number') {
      modifiedUsage.input_tokens = Math.round(usage.input_tokens * this.multiplier);
    }
    
    if (typeof usage.output_tokens === 'number') {
      modifiedUsage.output_tokens = Math.round(usage.output_tokens * this.multiplier);
    }

    console.log(`Token usage modified: ${JSON.stringify(usage)} -> ${JSON.stringify(modifiedUsage)}`);
    return modifiedUsage;
  }

  /**
   * 处理非流式响应
   * @param {Response} response - HTTP响应对象
   * @returns {Promise<Response>} - 修改后的响应
   */
  async transformResponseOut(response) {
    // 只处理JSON响应
    if (!response.headers.get("Content-Type")?.includes("application/json")) {
      return response;
    }

    try {
      const jsonResponse = await response.json();
      
      // 修改usage字段
      if (jsonResponse.usage) {
        jsonResponse.usage = this.multiplyUsage(jsonResponse.usage);
      }

      // 对于Anthropic格式的响应，usage可能在不同位置
      if (jsonResponse.message && jsonResponse.message.usage) {
        jsonResponse.message.usage = this.multiplyUsage(jsonResponse.message.usage);
      }

      // 创建新的响应
      return new Response(JSON.stringify(jsonResponse), {
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
      });
    } catch (error) {
      console.error("Error processing response in TokenMultiplierTransformer:", error);
      return response;
    }
  }

  /**
   * 处理流式响应
   * @param {ReadableStream} stream - 原始流
   * @returns {ReadableStream} - 修改后的流
   */
  transformStreamOut(stream) {
    const transformer = this;

    return new ReadableStream({
      start(controller) {
        const reader = stream.getReader();

        function pump() {
          return reader.read().then(({ done, value }) => {
            if (done) {
              controller.close();
              return;
            }

            try {
              // 处理SSE格式的数据
              if (typeof value === 'string') {
                // 解析SSE数据
                const lines = value.split('\n');
                let modifiedValue = '';

                for (const line of lines) {                  
                  console.log(`TokenMultiplierTransformer read Line: ${line}`);
                  if (line.startsWith('data: ') && line !== 'data: [DONE]') {
                    try {
                      const dataStr = line.slice(6); // 移除 'data: ' 前缀
                      const data = JSON.parse(dataStr);

                      // 修改usage字段
                      if (data.usage) {
                        data.usage = transformer.multiplyUsage(data.usage);
                      }

                      // 对于message_delta事件，usage可能在delta中
                      if (data.delta && data.delta.usage) {
                        data.delta.usage = transformer.multiplyUsage(data.delta.usage);
                      }

                      modifiedValue += 'data: ' + JSON.stringify(data) + '\n';
                    } catch (parseError) {
                      // 如果解析失败，保持原样
                      modifiedValue += line + '\n';
                    }
                  } else {
                    modifiedValue += line + '\n';
                  }
                }

                controller.enqueue(modifiedValue);
              } else if (value && typeof value === 'object') {
                // 处理对象格式的流数据（如TransformStream处理后的数据）
                const modifiedValue = { ...value };

                // 修改usage字段
                if (modifiedValue.data && modifiedValue.data.usage) {
                  modifiedValue.data.usage = transformer.multiplyUsage(modifiedValue.data.usage);
                }

                // 对于message_delta事件
                if (modifiedValue.data && modifiedValue.data.delta && modifiedValue.data.delta.usage) {
                  modifiedValue.data.delta.usage = transformer.multiplyUsage(modifiedValue.data.delta.usage);
                }

                controller.enqueue(modifiedValue);
              } else {
                // 对于其他类型数据，直接传递
                controller.enqueue(value);
              }
            } catch (error) {
              console.error("Error processing stream chunk in TokenMultiplierTransformer:", error);
              controller.enqueue(value);
            }

            return pump();
          });
        }

        return pump();
      }
    });
  }
}

// 导出transformer类
module.exports = TokenMultiplierTransformer;
